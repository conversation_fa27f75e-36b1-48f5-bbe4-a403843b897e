package org.swallow.work.dao.hibernate;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.orm.hibernate5.HibernateTemplate;
import org.hibernate.SessionFactory;
import org.swallow.exception.SwtException;
import org.swallow.work.model.Sweep;

/**
 * Test class for SweepSearchDAOHibernate to verify SQL injection fixes
 */
@RunWith(MockitoJUnitRunner.class)
public class SweepSearchDAOHibernateTest {

    @Mock
    private SessionFactory sessionFactory;
    
    @Mock
    private HibernateTemplate hibernateTemplate;
    
    @InjectMocks
    private SweepSearchDAOHibernate sweepSearchDAO;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        sweepSearchDAO.setSessionFactory(sessionFactory);
        // Use reflection to set the hibernateTemplate
        try {
            java.lang.reflect.Field field = org.springframework.orm.hibernate5.support.HibernateDaoSupport.class.getDeclaredField("hibernateTemplate");
            field.setAccessible(true);
            field.set(sweepSearchDAO, hibernateTemplate);
        } catch (Exception e) {
            // Handle reflection exception
        }
    }
    
    @Test
    public void testFetchSweepDetailsWithBindVariables() {
        // Arrange
        Long sweepId = 12345L;
        String hostId = "HOST001";
        List<Object> expectedResult = new ArrayList<>();

        when(hibernateTemplate.find(
            eq("from Sweep sweep where sweep.id.sweepId = ?0 and sweep.id.hostId = ?1"),
            any(Object[].class)
        )).thenReturn(expectedResult);

        // Act
        Collection result = sweepSearchDAO.fetchsweepdetails(sweepId, hostId);

        // Assert
        assertNotNull(result);
        verify(hibernateTemplate).find(
            eq("from Sweep sweep where sweep.id.sweepId = ?0 and sweep.id.hostId = ?1"),
            eq(new Object[]{sweepId, hostId})
        );
    }
    
    @Test
    public void testFetchSweepDetailsArchiveWithBindVariables() {
        // Arrange
        Long sweepId = 12345L;
        String hostId = "HOST001";
        String archiveId = null; // Test non-archive path
        List<Object> expectedResult = new ArrayList<>();

        when(hibernateTemplate.find(
            eq("from Sweep sweep where sweep.id.sweepId = ?0 and sweep.id.hostId = ?1"),
            any(Object[].class)
        )).thenReturn(expectedResult);

        // Act
        Collection result = sweepSearchDAO.fetchsweepdetailsArchive(sweepId, hostId, archiveId);

        // Assert
        assertNotNull(result);
        verify(hibernateTemplate).find(
            eq("from Sweep sweep where sweep.id.sweepId = ?0 and sweep.id.hostId = ?1"),
            eq(new Object[]{sweepId, hostId})
        );
    }
    
    @Test
    public void testFetchMovementDetailsWithBindVariables() {
        // Arrange
        String hostId = "HOST001";
        String entityId = "ENTITY001";
        Long movementId = 67890L;
        List<Object> expectedResult = new ArrayList<>();
        
        when(hibernateTemplate.find(
            eq(" from Movement mov where mov.id.hostId = ?0 and mov.id.entityId = ?1 and mov.id.movementId = ?2"),
            any(Object[].class)
        )).thenReturn(expectedResult);
        
        // Act
        Collection result = sweepSearchDAO.fetchMovementDetails(hostId, entityId, movementId);
        
        // Assert
        assertNotNull(result);
        verify(hibernateTemplate).find(
            eq(" from Movement mov where mov.id.hostId = ?0 and mov.id.entityId = ?1 and mov.id.movementId = ?2"),
            eq(new Object[]{hostId, entityId, movementId})
        );
    }
    
    @Test
    public void testFetchDetailsBasicParametersWithBindVariables() throws SwtException {
        // Arrange
        String sortOrder = "ASC";
        String acctType = "";
        String status = "A";
        String type = "A";
        String currency = "";
        String message = "";
        String accountId = "ACC001";
        String bookCode = "";
        String inputUser = "";
        String authUser = "";
        String subUser = "";
        String entityId = "ENTITY001";
        String hostId = "HOST001";
        String postCutOff = "N";
        Double amountOver = 0.0;
        Double amountUnder = 0.0;
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";
        String format = "DELIMITED";
        Object[] currencyCodeArray = {"USD", "EUR"};
        String archiveId = null; // Test non-archive path
        
        List<Object> expectedResult = new ArrayList<>();
        
        when(hibernateTemplate.find(anyString(), any(Object[].class)))
            .thenReturn(expectedResult);
        
        // Act
        Collection result = sweepSearchDAO.fetchdetails(
            sortOrder, acctType, status, type, currency, message, accountId, 
            bookCode, inputUser, authUser, subUser, entityId, hostId, 
            postCutOff, amountOver, amountUnder, fromDate, toDate, format, 
            currencyCodeArray, archiveId
        );
        
        // Assert
        assertNotNull(result);
        // Verify that hibernateTemplate.find was called with parameterized query
        verify(hibernateTemplate, atLeastOnce()).find(anyString(), any(Object[].class));
    }
    
    @Test
    public void testSqlInjectionPrevention() {
        // Arrange - Test with malicious input
        Long maliciousSweepId = 12345L;
        String maliciousHostId = "HOST001'; DROP TABLE sweep; --";
        List<Object> expectedResult = new ArrayList<>();

        when(hibernateTemplate.find(anyString(), any(Object[].class)))
            .thenReturn(expectedResult);

        // Act
        Collection result = sweepSearchDAO.fetchsweepdetails(maliciousSweepId, maliciousHostId);

        // Assert
        assertNotNull(result);
        // Verify that the malicious input is passed as a parameter, not concatenated
        verify(hibernateTemplate).find(
            eq("from Sweep sweep where sweep.id.sweepId = ?0 and sweep.id.hostId = ?1"),
            eq(new Object[]{maliciousSweepId, maliciousHostId})
        );
    }

    @Test
    public void testAmountComparisonWithBindVariables() throws SwtException {
        // Arrange - Test amount comparison with proper numeric parameters
        String sortOrder = "ASC";
        String acctType = "";
        String status = "N";
        String type = "A";
        String currency = "";
        String message = "";
        String accountId = "";
        String bookCode = "";
        String inputUser = "";
        String authUser = "";
        String subUser = "";
        String entityId = "ENTITY001";
        String hostId = "HOST001";
        String postCutOff = "N";
        Double amountOver = 1000.50; // Test with decimal amounts
        Double amountUnder = 5000.75;
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";
        String format = "DELIMITED";
        Object[] currencyCodeArray = {"USD"};
        String archiveId = null;

        List<Object> expectedResult = new ArrayList<>();

        when(hibernateTemplate.find(anyString(), any(Object[].class)))
            .thenReturn(expectedResult);

        // Act
        Collection result = sweepSearchDAO.fetchdetails(
            sortOrder, acctType, status, type, currency, message, accountId,
            bookCode, inputUser, authUser, subUser, entityId, hostId,
            postCutOff, amountOver, amountUnder, fromDate, toDate, format,
            currencyCodeArray, archiveId
        );

        // Assert
        assertNotNull(result);
        // Verify that hibernateTemplate.find was called with parameterized query
        // The amounts should be passed as Double objects, not concatenated strings
        verify(hibernateTemplate, atLeastOnce()).find(anyString(), any(Object[].class));
    }

    @Test
    public void testAmountOverOnlyWithBindVariables() throws SwtException {
        // Arrange - Test amount over only (amountUnder = 0)
        String sortOrder = "ASC";
        String acctType = "";
        String status = "S";
        String type = "M";
        String currency = "";
        String message = "";
        String accountId = "";
        String bookCode = "";
        String inputUser = "";
        String authUser = "";
        String subUser = "";
        String entityId = "ENTITY001";
        String hostId = "HOST001";
        String postCutOff = "N";
        Double amountOver = 2500.25; // Test with decimal amount
        Double amountUnder = 0.0; // This should trigger calculateamount1
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";
        String format = "DELIMITED";
        Object[] currencyCodeArray = {"EUR"};
        String archiveId = null;

        List<Object> expectedResult = new ArrayList<>();

        when(hibernateTemplate.find(anyString(), any(Object[].class)))
            .thenReturn(expectedResult);

        // Act
        Collection result = sweepSearchDAO.fetchdetails(
            sortOrder, acctType, status, type, currency, message, accountId,
            bookCode, inputUser, authUser, subUser, entityId, hostId,
            postCutOff, amountOver, amountUnder, fromDate, toDate, format,
            currencyCodeArray, archiveId
        );

        // Assert
        assertNotNull(result);
        // Verify that hibernateTemplate.find was called with parameterized query
        verify(hibernateTemplate, atLeastOnce()).find(anyString(), any(Object[].class));
    }
}
